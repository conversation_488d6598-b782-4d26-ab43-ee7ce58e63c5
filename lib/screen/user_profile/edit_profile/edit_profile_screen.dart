import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/user/edit_profile/edit_profile_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class EditProfileScreen extends StatefulWidget {
  final UserModel? user;

  const EditProfileScreen({super.key, this.user});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  late EditProfileCubit _editProfileCubit;
  late TextEditingController _displayNameController;
  late TextEditingController _bioController;

  @override
  void initState() {
    super.initState();
    _editProfileCubit = EditProfileCubit();
    _displayNameController = TextEditingController();
    _bioController = TextEditingController();

    if (widget.user != null) {
      _editProfileCubit.initializeProfile(widget.user!);
      _displayNameController.text =
          widget.user!.fullName ?? widget.user!.firstName ?? '';
      _bioController.text = widget.user!.moreInfo?['bio'] as String? ?? '';
    }

    // Add listener to update character count
    _bioController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _bioController.dispose();
    _editProfileCubit.resetState();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _editProfileCubit,
      child: BlocConsumer<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Profile updated successfully')),
            );
            context.pop();
          } else if (state.status.isFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'Failed to update profile'),
              ),
            );
          }
        },
        builder: (context, state) {
          return Bas(
            backgroundColor: const Color(0xFF0F0F0F),
            body: Stack(
              children: [
                // Background image
                Positioned.fill(
                  child: Assets.images.defaultBackground.image(
                    fit: BoxFit.cover,
                  ),
                ),
                // Gradient overlays
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          const Color(0xFF0F0F0F).withValues(alpha: 0.8),
                        ],
                      ),
                    ),
                  ),
                ),
                // Main content
                SafeArea(
                  child: Column(
                    children: [
                      _buildAppBar(context, state),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Column(
                              children: [
                                const SizedBox(height: 24),
                                _buildFormCard(state),
                                const SizedBox(height: 24),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAppBar(BuildContext context, EditProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Close button
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () => context.pop(),
                child: Container(
                  width: 32,
                  height: 32,
                  padding: const EdgeInsets.all(6),
                  child: const Icon(Icons.close, color: Colors.white, size: 20),
                ),
              ),
            ),
          ),
          // Title
          Expanded(
            child: Text(
              'Edit profile',
              style: titleLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // Save button
          Container(
            decoration: BoxDecoration(
              color:
                  state.hasChanges && !state.status.isLoading
                      ? context.themeData.primaryGreen500
                      : const Color(0xFFF6F6F6),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(24),
                onTap:
                    state.hasChanges && !state.status.isLoading
                        ? () => _saveProfile()
                        : null,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child:
                      state.status.isLoading
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                          : Text(
                            'Save',
                            style: titleMedium.copyWith(
                              color:
                                  state.hasChanges && !state.status.isLoading
                                      ? Colors.white
                                      : const Color(0xFFAFAFAF),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormCard(EditProfileState state) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildAvatarSection(state),
          const SizedBox(height: 24),
          _buildFormFields(state),
        ],
      ),
    );
  }

  Widget _buildAvatarSection(EditProfileState state) {
    return Center(
      child: Stack(
        children: [
          // Avatar container with border
          Container(
            width: 64,
            height: 64,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(32),
              border: Border.all(color: const Color(0xFFAFAFAF), width: 1),
            ),
            child: Stack(
              children: [
                // Avatar image
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white, width: 1),
                      shape: BoxShape.circle,
                    ),
                    child: CircleAvatar(
                      radius: 32,
                      backgroundImage: NetworkImage(
                        state.avatar ??
                            widget.user?.avatar ??
                            'https://randomuser.me/api/portraits/women/47.jpg',
                      ),
                    ),
                  ),
                ),
                // Camera icon overlay on avatar
                Positioned.fill(
                  child: GestureDetector(
                    onTap: _selectAvatar,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.3),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields(EditProfileState state) {
    return Column(
      children: [
        // Display Name Field
        _buildInputField(
          label: 'Display Name',
          controller: _displayNameController,
          hintText: widget.user?.fullName ?? widget.user?.firstName ?? '',
          onChanged: (value) => _editProfileCubit.updateDisplayName(value),
        ),
        const SizedBox(height: 6),

        // Username Field (Disabled)
        _buildInputField(
          label: 'Username',
          controller: null,
          hintText: widget.user?.username ?? '',
          isEnabled: false,
        ),
        const SizedBox(height: 6),

        // Bio Field
        _buildBioField(state),
      ],
    );
  }

  Widget _buildInputField({
    required String label,
    required TextEditingController? controller,
    required String hintText,
    bool isEnabled = true,
    ValueChanged<String>? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 5, bottom: 6),
          child: Text(
            label,
            style: titleMedium.copyWith(
              color: const Color(0xFF292929),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 14),
          decoration: BoxDecoration(
            color: isEnabled ? Colors.white : const Color(0xFFF6F6F6),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isEnabled ? const Color(0xFFE5E5E5) : Colors.transparent,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child:
                    isEnabled && controller != null
                        ? TextField(
                          controller: controller,
                          onChanged: onChanged,
                          style: bodyMedium.copyWith(
                            color: const Color(0xFF292929),
                          ),
                          decoration: InputDecoration(
                            hintText: hintText,
                            hintStyle: bodyMedium.copyWith(
                              color: const Color(0xFF777777),
                            ),
                            border: InputBorder.none,
                            isDense: true,
                            contentPadding: EdgeInsets.zero,
                          ),
                        )
                        : Text(
                          hintText,
                          style: bodyMedium.copyWith(
                            color: const Color(0xFF777777),
                          ),
                        ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBioField(EditProfileState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 5, bottom: 6),
          child: Text(
            'Bio',
            style: titleMedium.copyWith(
              color: const Color(0xFF292929),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFFE5E5E5)),
          ),
          child: Column(
            children: [
              TextField(
                controller: _bioController,
                onChanged: (value) => _editProfileCubit.updateBio(value),
                maxLines: 4,
                style: bodyMedium.copyWith(color: const Color(0xFF292929)),
                decoration: InputDecoration(
                  hintText: 'Write something short about you...',
                  hintStyle: bodyMedium.copyWith(
                    color: const Color(0xFF777777),
                  ),
                  border: InputBorder.none,
                  isDense: true,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const SizedBox(height: 5),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    '${_bioController.text.length}/120',
                    style: labelSmall.copyWith(color: const Color(0xFF777777)),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _selectAvatar() {
    // TODO: Implement avatar selection (camera/gallery)
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Avatar selection coming soon')),
    );
  }

  void _saveProfile() {
    if (widget.user?.id != null) {
      _editProfileCubit.saveProfile(widget.user!.id);
    }
  }
}
