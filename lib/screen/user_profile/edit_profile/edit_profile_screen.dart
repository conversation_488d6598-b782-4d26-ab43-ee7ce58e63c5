import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/user/edit_profile/edit_profile_cubit.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class EditProfileScreen extends StatefulWidget {
  final UserModel? user;

  const EditProfileScreen({super.key, this.user});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  late EditProfileCubit _editProfileCubit;
  late TextEditingController _displayNameController;
  late TextEditingController _bioController;

  @override
  void initState() {
    super.initState();
    _editProfileCubit =  EditProfileCubit();
    _displayNameController = TextEditingController();
    _bioController = TextEditingController();

    if (widget.user != null) {
      _editProfileCubit.initializeProfile(widget.user!);
      _displayNameController.text =
          widget.user!.fullName ?? widget.user!.firstName ?? '';
      _bioController.text = widget.user!.moreInfo?['bio'] as String? ?? '';
    }
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _bioController.dispose();
    _editProfileCubit.resetState();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return BlocProvider.value(
      value: _editProfileCubit,
      child: BlocConsumer<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Profile updated successfully')),
            );
            context.pop();
          } else if (state.status.isFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'Failed to update profile'),
              ),
            );
          }
        },
        builder: (context, state) {
          return Scaffold(
            backgroundColor: themeData.neutral50,
            appBar: BaseAppBar(
              title: 'Edit profile',
              centerTitle: true,
              leading: _buildCloseButton(context),
              actions: [
                Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: TSButton.primary(
                    title: 'Save',
                    size: ButtonSize.small,
                    isEnabled: state.hasChanges && !state.status.isLoading,
                    isLoading: state.status.isLoading,
                    onPressed: () => _saveProfile(),
                  ),
                ),
              ],
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildAvatarSection(state),
                  const SizedBox(height: 24),
                  _buildFormFields(state),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8),
      child: Material(
        color: context.themeData.neutral100.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => context.pop(),
          child: Container(
            width: 32,
            height: 32,
            padding: const EdgeInsets.all(6),
            child: Icon(
              Icons.close,
              color: context.themeData.textPrimary,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatarSection(EditProfileState state) {
    return Column(
      children: [
        Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.white, width: 4),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: CircleAvatar(
                radius: 48,
                backgroundImage: NetworkImage(
                  state.avatar ??
                      widget.user?.avatar ??
                      'https://randomuser.me/api/portraits/women/47.jpg',
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: context.themeData.primaryGreen500,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: InkWell(
                  onTap: _selectAvatar,
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    width: 32,
                    height: 32,
                    padding: const EdgeInsets.all(6),
                    child: Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFormFields(EditProfileState state) {
    return Column(
      children: [
        // Display Name Field
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 5, bottom: 6),
              child: Text(
                'Display Name',
                style: titleMedium.copyWith(
                  color: context.themeData.neutral800,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            TTextField(
              textController: _displayNameController,
              hintText: 'Enter your display name',
              onChanged: (value) => _editProfileCubit.updateDisplayName(value),
            ),
          ],
        ),
        const SizedBox(height: 20),

        // Username Field (Disabled)
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 5, bottom: 6),
              child: Text(
                'Username',
                style: titleMedium.copyWith(
                  color: context.themeData.neutral800,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
              decoration: BoxDecoration(
                color: context.themeData.neutral100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: context.themeData.neutral200),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.user?.username ?? '',
                      style: bodyMedium.copyWith(
                        color: context.themeData.neutral400,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),

        // Bio Field
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 5, bottom: 6),
              child: Text(
                'Bio',
                style: titleMedium.copyWith(
                  color: context.themeData.neutral800,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            TTextField(
              textController: _bioController,
              hintText: 'Write something short about you...',
              maxLines: 4,
              onChanged: (value) => _editProfileCubit.updateBio(value),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 8, right: 5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    '${_bioController.text.length}/120',
                    style: labelSmall.copyWith(
                      color: context.themeData.neutral400,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _selectAvatar() {
    // TODO: Implement avatar selection (camera/gallery)
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Avatar selection coming soon')),
    );
  }

  void _saveProfile() {
    if (widget.user?.id != null) {
      _editProfileCubit.saveProfile(widget.user!.id);
    }
  }
}
